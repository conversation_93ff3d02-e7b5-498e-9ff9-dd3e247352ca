from fastapi import APIRouter
from app.models.feedback_model import FeedbackRequest
from app.modules.services.feedback_service_v2 import process_feedback_submission_v2
from app.core.logger import logger

router = APIRouter()

@router.post("/v2/feedback")
async def submit_feedback_v2(request: FeedbackRequest):
    logger.info(f"Received non-encrypted feedback request (V2) | action_ref_id={request.action_ref_id} | feedback_provided={request.feedback_provided}")
    return await process_feedback_submission_v2(
        feedback_provided=request.feedback_provided,
        action_ref_id=request.action_ref_id,
        feedback_category=request.feedback_category,
        feedback_notes=request.feedback_notes
    )
