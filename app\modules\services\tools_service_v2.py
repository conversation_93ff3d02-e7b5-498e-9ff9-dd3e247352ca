from fastapi import <PERSON><PERSON><PERSON>Exception
from app.connections.llm_request_handler import <PERSON>MRequestHandler
import os
import datetime
import time
from app.modules.services.action_tracker import ActionTracker
from app.utils.background_tasks import BackgroundTaskManager
from app.utils.llm_helpers import get_action_url
from app.core.logger import logger, log_api_transaction
from app.modules.services.session_service import SessionService
from app.models.tools_model import ToolParameters
from app.utils.feedback_helpers import process_feedback
from app.core.config import CURRENCY_CODE

session_service = SessionService()

@log_api_transaction(action_type="tool_action", include_payload=True, include_response=True)
async def process_tool_action_v2(action_type: str, client_id: str, documentversion_id: int, parameters: dict):
    """Non-encrypted version of process_tool_action"""
    logger.info(f"Processing tool action (V2) | action_type={action_type} | client_id={client_id} | documentversion_id={documentversion_id}")
    
    try:
        session = session_service.get_session_by_document_id(documentversion_id)
        if not session:
            session = session_service.create_session(documentversion_id)
            await handle_initial_summary_action_v2(client_id, documentversion_id, parameters, session.id)
        
        if action_type in ["undo", "insert&edit"]:
            return await handle_non_llm_action_v2(action_type, parameters, session.id)
        
        return await handle_llm_action_v2(action_type, parameters, session.id)
            
    except Exception as e:
        logger.error(f"Error processing tool action (V2) | action_type={action_type} | documentversion_id={documentversion_id} | error={str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing tool action: {str(e)}")

async def handle_non_llm_action_v2(action_type: str, parameters: dict, session_id: int) -> dict:
    """Non-encrypted version of handle_non_llm_action"""
    action_ref_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    tracker = ActionTracker()
    await tracker.track_action(
        session_id=session_id,
        action_type=action_type,
        start_time=time.time(),
        prompt_data=parameters,
        response_data="",
        action_ref_id=action_ref_id
    )
    logger.info(f"Non-LLM action recorded (V2) | action_type={action_type} | session_id={session_id} | action_ref_id={action_ref_id}")
    return {
        "status": "success", 
        "message": f"{action_type} action recorded", 
        "action_ref_id": action_ref_id
    }

async def handle_llm_action_v2(action_type: str, parameters: dict, session_id: int) -> dict:
    """Non-encrypted version of handle_llm_action"""
    start_time = time.time()
    action_ref_id = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    feedback_applied = False
  
    if action_type == "regenerate" and parameters.get("action_ref_id"):
        feedback_applied = await process_feedback(parameters)
    
    tool_params = ToolParameters(**parameters)
    tool_params.validate_for_action(action_type)
    
    full_url = get_action_url(action_type)
    
    if feedback_applied and action_type == "regenerate":
        full_url = get_action_url("finetune")
    
    tool_params_dict = tool_params.dict()
    if isinstance(tool_params_dict.get("needs"), str):
        tool_params_dict["needs"] = [item.strip() for item in tool_params_dict["needs"].split("|") if item.strip()]

    async with LLMRequestHandler() as llm_handler:
        response = await llm_handler.make_request(
            method="POST",
            url=full_url,
            payload=tool_params_dict,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {os.getenv('API_KEY')}"
            }
        )
        
        tracker = ActionTracker()
        BackgroundTaskManager.create_task(
            tracker.track_action(
                session_id=session_id,
                action_type=action_type,
                start_time=start_time,
                prompt_data=parameters,
                response_data=response,
                action_ref_id=action_ref_id
            )
        )
        
        # Track LLM token costs
        async def _send_llm_token_costs():
            from app.connections.analytics_handler import AnalyticsHandler
            token_data = {
                "session_id": session_id,
                "action_ref_id": action_ref_id,
                "input_tokens": response.get("token_usage", {}).get("input_tokens", 0),
                "output_tokens": response.get("token_usage", {}).get("output_tokens", 0),
                "currency": CURRENCY_CODE
            }
            async with AnalyticsHandler() as analytics:
                await analytics.track_llm_token_costs(token_data)

        BackgroundTaskManager.create_task(_send_llm_token_costs())

        response["action_ref_id"] = action_ref_id
        logger.info(f"LLM action completed (V2) | action_type={action_type} | session_id={session_id} | action_ref_id={action_ref_id}")
        return response

async def handle_initial_summary_action_v2(client_id: str, documentversion_id: int, parameters: dict, session_id: int):
    """Non-encrypted version of handle_initial_summary_action"""
    try:
        from app.modules.services.analytics_service import SummaryAnalyticsService
        analytics_service = SummaryAnalyticsService()
        await analytics_service.track_initial_action(
            client_id=client_id,
            session_id=session_id,
            clinician_type=parameters.get("clinician_type"),
            procedure_code=parameters.get("procedure_code")
        )
        logger.info(f"Initial summary action tracked (V2) | client_id={client_id} | session_id={session_id} | documentversion_id={documentversion_id}")
    except Exception as e:
        logger.warning(f"Error handling initial summary action (V2) | client_id={client_id} | session_id={session_id} | error={str(e)}")
