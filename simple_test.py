#!/usr/bin/env python3
"""
Simple test to check if the feedback endpoint is working
"""
import requests
import json

BASE_URL = "http://localhost:8080/api/v1"
API_KEY = "alpha_tool_1rtxWMBD0B4urvXmJCKR3h9UE55zt5K5vOfcnZt3tHf9Oc3kkKzO5RT0RSJhwFps"

def test_endpoint():
    try:
        # Test with simple JSON data
        response = requests.post(
            f"{BASE_URL}/feedback",
            json={"test": "data"},
            headers={
                "Content-Type": "application/json",
                "X-API-Key": API_KEY
            },
            timeout=10
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_endpoint()
