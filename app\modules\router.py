from fastapi import APIRouter, Depends
from app.modules.routers.tools import router as tools_router
from app.modules.routers.feedback import router as feedback_router
from app.modules.routers.tools_v1 import router as tools_v1_router
from app.modules.routers.feedback_v1 import router as feedback_v1_router
from app.modules.routers.tools_v2 import router as tools_v2_router
from app.modules.routers.feedback_v2 import router as feedback_v2_router
from app.modules.routers.time_saved import router as time_saved_router
from app.modules.routers.public_key import router as key_router
from app.core.auth import verify_api_key
from app.core.config import API_PREFIX



router = APIRouter(prefix=API_PREFIX, tags=["tools"], dependencies=[Depends(verify_api_key)])

# Legacy routers (for backward compatibility)
router.include_router(tools_router)
router.include_router(feedback_router)

# V1 routers (encrypted)
router.include_router(tools_v1_router)
router.include_router(feedback_v1_router)

# V2 routers (non-encrypted)
router.include_router(tools_v2_router)
router.include_router(feedback_v2_router)

# Other routers
router.include_router(time_saved_router)
router.include_router(key_router)


