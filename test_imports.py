#!/usr/bin/env python3
"""
Test imports to check for any issues
"""

try:
    print("Testing imports...")
    
    print("1. Testing crypto_engine_decorator import...")
    from app.utils.crypto_engine_decorator import secure_payload
    print("✅ crypto_engine_decorator imported successfully")
    
    print("2. Testing feedback_service import...")
    from app.modules.services.feedback_service import process_feedback_submission
    print("✅ feedback_service imported successfully")
    
    print("3. Testing feedback router import...")
    from app.modules.routers.feedback import router
    print("✅ feedback router imported successfully")
    
    print("4. Testing main app import...")
    from main import app
    print("✅ main app imported successfully")
    
    print("\n🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
