import os
import base64
import json
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import Secret<PERSON>lient
from app.core.logger import logger
from app.core.config import (
    VAULT_URL,
    CLIENT_ID,
    CLIENT_SECRET,
    TENANT_ID,
    AZURE_KV_RSA_PRIVATE_KEY_NAME,
    AZURE_KV_RSA_PUBLIC_KEY_NAME,
)

try:
    credential = ClientSecretCredential(
        tenant_id=TENANT_ID, client_id=CLIENT_ID, client_secret=CLIENT_SECRET
    )

    azure_kv_client = SecretClient(vault_url=VAULT_URL, credential=credential)

    PRIVATE_KEY_PEM = azure_kv_client.get_secret(
        AZURE_KV_RSA_PRIVATE_KEY_NAME
    ).value.encode()
    PUBLIC_KEY_PEM = azure_kv_client.get_secret(AZURE_KV_RSA_PUBLIC_KEY_NAME).value.encode()

    private_key = serialization.load_pem_private_key(PRIVATE_KEY_PEM, password=None)
    public_key = serialization.load_pem_public_key(PUBLIC_KEY_PEM)

except Exception as e:
    logger.critical(f"Key initialization failed: {e}")


def decrypt_payload(encrypted_data: str, encrypted_key: str, nonce: str) -> dict:
    logger.info("Starting decryption of payload.")

    try:
        logger.debug("Decrypting AES key using RSA private key.")
        aes_key = private_key.decrypt(
            base64.b64decode(encrypted_key),
            padding.OAEP(
                mgf=padding.MGF1(algorithm=hashes.SHA256()),
                algorithm=hashes.SHA256(),
                label=None,
            ),
        )

        logger.debug("Decoding nonce and encrypted payload.")
        nonce_bytes = base64.b64decode(nonce)
        encrypted_data_bytes = base64.b64decode(encrypted_data)
        ciphertext = encrypted_data_bytes[:-16]
        tag = encrypted_data_bytes[-16:]

        logger.debug("Initializing AES-GCM decryption.")
        cipher = Cipher(
            algorithms.AES(aes_key), modes.GCM(nonce_bytes, tag), backend=default_backend()
        )
        decryptor = cipher.decryptor()

        decrypted = decryptor.update(ciphertext) + decryptor.finalize()
        logger.info("Payload decrypted successfully.")
        return json.loads(decrypted.decode())

    except Exception as e:
        logger.error(f"Decryption failed: {e}")
        raise

def encrypt_response(response_data: dict, aes_key: bytes) -> tuple[str, str]:
    logger.info("Starting encryption of response data.")

    try:
        response_nonce = os.urandom(12)
        response_json = json.dumps(response_data).encode()

        logger.debug("Initializing AES-GCM encryption.")
        cipher = Cipher(
            algorithms.AES(aes_key), modes.GCM(response_nonce), backend=default_backend()
        )
        encryptor = cipher.encryptor()
        encrypted_output = encryptor.update(response_json) + encryptor.finalize()
        encrypted_combined = encrypted_output + encryptor.tag

        logger.info("Response data encrypted successfully.")
        return (
            base64.b64encode(encrypted_combined).decode(),
            base64.b64encode(response_nonce).decode(),
        )

    except Exception as e:
        logger.error(f"Encryption failed: {e}")
        raise