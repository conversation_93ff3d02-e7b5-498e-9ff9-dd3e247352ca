from fastapi import HTT<PERSON>Exception
from app.connections.analytics_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.logger import logger
from app.modules.services.session_service import SessionService
from app.modules.services.summary_db_service import SummaryDBService
from typing import Optional

session_service = SessionService()

async def process_feedback_submission_v2(feedback_provided: int, action_ref_id: str,
                                       feedback_category: Optional[str] = None, feedback_notes: Optional[str] = None):
    """Non-encrypted version of process_feedback_submission"""
    logger.info(f"Processing feedback submission (V2) | action_ref_id={action_ref_id} | feedback_provided={feedback_provided}")

    try:
        # Validate feedback_provided value
        if feedback_provided not in [0, 1, 2]:
            logger.error(f"Invalid feedback_provided value: {feedback_provided}")
            raise HTTPException(
                status_code=400, 
                detail="feedback_provided must be 0 (no feedback), 1 (thumbs-down), or 2 (thumbs-up)"
            )

        # Prepare feedback data
        feedback_data = {
            "feedback_provided": feedback_provided,
            "feedback_category": feedback_category,
            "feedback_notes": feedback_notes,
            "action_ref_id": action_ref_id
        }

        # Send to analytics
        async with <PERSON>lyticsHandler() as analytics:
            api_result = await analytics.track_feedback(feedback_data)

        # Store in summary database
        await SummaryDBService.store_feedback(
            feedback_provided=feedback_provided,
            action_ref_id=action_ref_id,
            feedback_category=feedback_category,
            feedback_notes=feedback_notes
        )

        logger.info(f"Feedback submission completed (V2) | action_ref_id={action_ref_id}")
        return api_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing feedback submission (V2) | action_ref_id={action_ref_id} | error={str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing feedback submission: {str(e)}")
