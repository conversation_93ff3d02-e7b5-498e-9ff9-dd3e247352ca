#!/usr/bin/env python3
"""
Test script to verify versioned router implementation
"""

try:
    print("Testing versioned router imports...")
    
    print("1. Testing V1 routers (encrypted)...")
    from app.modules.routers.tools_v1 import router as tools_v1_router
    print("✅ tools_v1 router imported successfully")
    
    from app.modules.routers.feedback_v1 import router as feedback_v1_router
    print("✅ feedback_v1 router imported successfully")
    
    print("2. Testing V2 routers (non-encrypted)...")
    from app.modules.routers.tools_v2 import router as tools_v2_router
    print("✅ tools_v2 router imported successfully")
    
    from app.modules.routers.feedback_v2 import router as feedback_v2_router
    print("✅ feedback_v2 router imported successfully")
    
    print("3. Testing V2 services...")
    from app.modules.services.tools_service_v2 import process_tool_action_v2
    print("✅ tools_service_v2 imported successfully")
    
    from app.modules.services.feedback_service_v2 import process_feedback_submission_v2
    print("✅ feedback_service_v2 imported successfully")
    
    print("4. Testing main router...")
    from app.modules.router import router
    print("✅ main router imported successfully")
    
    print("5. Testing models...")
    from app.models.tools_model import ToolActionRequest
    from app.models.feedback_model import FeedbackRequest
    print("✅ models imported successfully")
    
    print("\n🎉 All versioned imports successful!")
    
    # Test route paths
    print("\n📍 Available routes:")
    print("V1 (Encrypted):")
    print("  - POST /api/v1/v1/tool-action")
    print("  - POST /api/v1/v1/feedback")
    print("V2 (Non-encrypted):")
    print("  - POST /api/v1/v2/tool-action")
    print("  - POST /api/v1/v2/feedback")
    print("Legacy (Encrypted):")
    print("  - POST /api/v1/tool-action")
    print("  - POST /api/v1/feedback")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
