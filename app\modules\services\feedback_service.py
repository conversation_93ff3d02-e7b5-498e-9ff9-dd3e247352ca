from fastapi import HTTP<PERSON>x<PERSON>, Request
from app.connections.analytics_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.logger import logger
from app.modules.services.session_service import SessionService
from app.modules.services.summary_db_service import SummaryDBService
from app.utils.crypto_engine_decorator import secure_payload
from typing import Optional

session_service = SessionService()

@secure_payload
async def process_feedback_submission(feedback_provided: int, action_ref_id: str,
                                    feedback_category: Optional[str] = None, feedback_notes: Optional[str] = None):
   
    logger.info(f"Processing feedback submission | action_ref_id={action_ref_id} | feedback_provided={feedback_provided}")

    try:
      
        if feedback_provided not in [0, 1, 2]:
            logger.error(f"Invalid feedback_provided value: {feedback_provided}")
            raise HTTPException(
                status_code=400, 
                detail="feedback_provided must be 0 (no feedback), 1 (thumbs-down), or 2 (thumbs-up)"
            )

       
        feedback_data = {
            "feedback_provided": feedback_provided,
            "feedback_category": feedback_category,
            "feedback_notes": feedback_notes,
            "action_ref_id": action_ref_id
        }

     
        async with <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>() as analytics:
            api_result = await analytics.track_feedback(feedback_data)


        await SummaryDBService.store_feedback(
            feedback_provided=feedback_provided,
            action_ref_id=action_ref_id,
            feedback_category=feedback_category,
            feedback_notes=feedback_notes
        )

        logger.info(f"Feedback submission completed successfully | action_ref_id={action_ref_id}")
        return api_result

    except HTTPException:
     
        raise
    except Exception as e:
        logger.error(f"Error processing feedback submission | action_ref_id={action_ref_id} | error={str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing feedback submission: {str(e)}")
